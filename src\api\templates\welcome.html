<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <!-- =====BOX ICONS===== -->
        <link href='https://cdn.jsdelivr.net/npm/boxicons@2.0.5/css/boxicons.min.css' rel='stylesheet'>

        <!-- ===== CSS ===== -->
        <link rel="stylesheet" href="{{ url_for('static', filename='css/styles.css') }}">
        <!-- Google Fonts -->
        <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600&display=swap" rel="stylesheet">
        <!-- Optional: Animate.css for card reveal -->
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/animate.css@4.1.1/animate.min.css">
        <!-- Bootstrap for enhanced styling -->
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <!-- VueJS for dynamic content -->
        <script src="https://cdn.jsdelivr.net/npm/vue@3/dist/vue.global.js"></script>
        <!-- Chart.js for analytics visualization -->
        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

        <title>Sisa Rasa</title>

        <style>
            /* Proper Google Fonts Integration */
            body {
                font-family: 'Poppins', sans-serif;
                font-weight: 400;
                line-height: 1.6;
            }

            h1, h2, h3, h4, h5, h6 {
                font-family: 'Poppins', sans-serif;
                font-weight: 600;
                line-height: 1.2;
            }

            .section__title {
                font-family: 'Poppins', sans-serif;
                font-weight: 600;
                font-size: 2rem;
                line-height: 1.2;
            }

            .section__subtitle {
                font-family: 'Poppins', sans-serif;
                font-weight: 400;
                font-size: 1.1rem;
                line-height: 1.4;
            }

            /* Responsive Typography */
            @media (max-width: 768px) {
                .section__title {
                    font-size: 1.8rem;
                }

                .section__subtitle {
                    font-size: 1rem;
                }

                body {
                    font-size: 0.9rem;
                    line-height: 1.5;
                }
            }

            @media (max-width: 480px) {
                .section__title {
                    font-size: 1.5rem;
                }

                .section__subtitle {
                    font-size: 0.9rem;
                }

                body {
                    font-size: 0.85rem;
                    line-height: 1.4;
                }
            }

            /* Prescriptive Analytics Styles - Proper Google Fonts */
            .trending, .leftovers {
                padding: 4rem 0;
                background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
                font-family: 'Poppins', sans-serif;
            }

            .trending__container, .leftovers__container {
                max-width: 1024px;
                margin: 0 auto;
                padding: 0 2rem;
                display: grid;
                grid-template-columns: 100%;
                width: calc(100% - 2rem);
                margin-left: auto;
                margin-right: auto;
                text-align: center;
            }

            .trending__content, .leftovers__content {
                text-align: center;
            }

            .trending__subtitle, .leftovers__subtitle {
                color: #6c757d;
                font-size: var(--normal-font-size);
                margin-bottom: 3rem;
                font-family: 'Poppins', sans-serif;
                font-weight: 400;
            }

            /* Ensure section titles use proper Google Fonts */
            .trending .section__title, .leftovers .section__title {
                font-size: 2rem;
                color: var(--white-color);
                margin-bottom: 1.5rem;
                text-align: center;
                font-family: 'Poppins', sans-serif;
                font-weight: 600;
            }

            /* Recipe Showcase */
            .recipe-showcase, .leftover-showcase {
                display: grid;
                gap: 3rem;
                margin-bottom: 3rem;
            }

            .recipe-section, .leftover-section {
                background: white;
                border-radius: 1rem;
                padding: 2rem;
                box-shadow: 0 4px 15px rgba(0,0,0,0.1);
                transition: transform 0.3s ease;
            }

            .recipe-section:hover, .leftover-section:hover {
                transform: translateY(-5px);
            }

            .recipe-section-title, .leftover-section-title {
                color: var(--white-color);
                font-size: 1.5rem;
                font-weight: 600;
                margin-bottom: 1.5rem;
                text-align: left;
                font-family: 'Poppins', sans-serif;
            }

            /* Recipe Cards */
            .recipe-cards {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
                gap: 1.5rem;
            }

            .recipe-card {
                background: linear-gradient(135deg, #f8f9fa, #e9ecef);
                border-radius: 0.75rem;
                padding: 1.5rem;
                position: relative;
                overflow: hidden;
                transition: all 0.3s ease;
                border: 2px solid transparent;
                cursor: pointer;
                box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            }

            .recipe-card:hover {
                border-color: #ea5e18;
                transform: translateY(-5px);
                box-shadow: 0 8px 25px rgba(234, 94, 24, 0.2);
            }

            .recipe-card-title {
                color: var(--white-color);
                font-size: 1.1rem;
                font-weight: 600;
                margin-bottom: 0.75rem;
                font-family: 'Poppins', sans-serif;
            }

            .recipe-card-description {
                color: #6c757d;
                font-size: var(--normal-font-size);
                margin-bottom: 1rem;
                font-family: 'Poppins', sans-serif;
                font-weight: 400;
            }

            .recipe-card-meta {
                display: flex;
                justify-content: space-between;
                align-items: center;
                font-size: 0.8rem;
                color: #6c757d;
                font-weight: 500;
                font-family: 'Poppins', sans-serif;
            }

            .recipe-card-ingredients {
                display: flex;
                flex-wrap: wrap;
                gap: 0.25rem;
                margin-bottom: 1rem;
            }

            .ingredient-chip {
                background: rgba(234, 94, 24, 0.8);
                color: white;
                padding: 0.2rem 0.5rem;
                border-radius: 0.5rem;
                font-size: 0.7rem;
                font-weight: 500;
                font-family: 'Poppins', sans-serif;
            }

            /* Chart Container */
            .chart-container {
                position: relative;
                height: 300px;
                width: 100%;
                margin: 1rem 0;
                background: white;
                border-radius: 0.75rem;
                padding: 1rem;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }

            /* Leftover Ingredients */
            .ingredient-tags {
                display: flex;
                flex-wrap: wrap;
                gap: 1rem;
                justify-content: center;
            }

            .ingredient-tag {
                background: linear-gradient(135deg, #ea5e18, #fedf2f);
                color: white;
                padding: 0.75rem 1.5rem;
                border-radius: 2rem;
                font-weight: 500;
                font-size: 0.9rem;
                font-family: 'Poppins', sans-serif;
                transition: all 0.3s ease;
                cursor: pointer;
                position: relative;
                overflow: hidden;
            }

            .ingredient-tag:hover {
                transform: translateY(-3px);
                box-shadow: 0 6px 20px rgba(234, 94, 24, 0.3);
            }

            .ingredient-tag::before {
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
                transition: left 0.5s;
            }

            .ingredient-tag:hover::before {
                left: 100%;
            }

            /* Combination Cards */
            .combination-cards {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 1.5rem;
            }

            .combination-card {
                background: linear-gradient(135deg, #083640, #0a4550);
                color: white;
                border-radius: 1rem;
                padding: 1.5rem;
                text-align: center;
                transition: all 0.3s ease;
                position: relative;
                overflow: hidden;
            }

            .combination-card:hover {
                transform: translateY(-5px);
                box-shadow: 0 10px 30px rgba(8, 54, 64, 0.3);
            }

            .combination-card::before {
                content: '';
                position: absolute;
                top: -50%;
                left: -50%;
                width: 200%;
                height: 200%;
                background: radial-gradient(circle, rgba(254, 223, 47, 0.1) 0%, transparent 70%);
                transition: all 0.3s ease;
                opacity: 0;
            }

            .combination-card:hover::before {
                opacity: 1;
                transform: scale(1.1);
            }

            .combination-title {
                font-size: 1.1rem;
                font-weight: 600;
                font-family: 'Poppins', sans-serif;
                margin-bottom: 0.75rem;
                position: relative;
                z-index: 1;
            }

            .combination-description {
                font-size: 0.9rem;
                font-family: 'Poppins', sans-serif;
                font-weight: 400;
                opacity: 0.9;
                margin-bottom: 1rem;
                position: relative;
                z-index: 1;
            }

            .combination-recipes {
                display: flex;
                flex-wrap: wrap;
                gap: 0.5rem;
                justify-content: center;
                position: relative;
                z-index: 1;
            }

            .recipe-chip {
                background: rgba(254, 223, 47, 0.2);
                color: #fedf2f;
                padding: 0.25rem 0.75rem;
                border-radius: 1rem;
                font-size: 0.8rem;
                font-family: 'Poppins', sans-serif;
                font-weight: 500;
                border: 1px solid rgba(254, 223, 47, 0.3);
            }

            /* Login Gate */
            .login-gate {
                position: relative;
                margin-top: 2rem;
            }

            .gate-overlay {
                background: rgba(8, 54, 64, 0.95);
                border-radius: 1rem;
                padding: 3rem 2rem;
                text-align: center;
                color: white;
                backdrop-filter: blur(10px);
            }

            .gate-icon {
                font-size: 3rem;
                color: #fedf2f;
                margin-bottom: 1rem;
            }

            .gate-overlay h4 {
                color: #fedf2f;
                font-size: 1.5rem;
                font-weight: 600;
                font-family: 'Poppins', sans-serif;
                margin-bottom: 1rem;
            }

            .gate-overlay p {
                color: rgba(255, 255, 255, 0.9);
                font-size: 1.1rem;
                font-family: 'Poppins', sans-serif;
                font-weight: 400;
                margin-bottom: 2rem;
            }

            .gate-button {
                display: inline-block;
                background: #ea5e18;
                color: white;
                padding: 0.75rem 2rem;
                border-radius: 2rem;
                text-decoration: none;
                font-weight: 500;
                font-family: 'Poppins', sans-serif;
                margin: 0 0.5rem;
                transition: all 0.3s ease;
                border: 2px solid #ea5e18;
            }

            .gate-button:hover {
                background: transparent;
                color: #ea5e18;
                transform: translateY(-2px);
            }

            .gate-button.secondary {
                background: transparent;
                color: #fedf2f;
                border-color: #fedf2f;
            }

            .gate-button.secondary:hover {
                background: #fedf2f;
                color: #083640;
            }

            /* Skeleton Loaders */
            .skeleton-loader {
                background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
                background-size: 200% 100%;
                animation: loading 1.5s infinite;
                border-radius: 0.5rem;
                height: 150px;
            }

            @keyframes loading {
                0% { background-position: 200% 0; }
                100% { background-position: -200% 0; }
            }

            .recipe-card-skeleton, .combination-card-skeleton, .ingredient-tag-skeleton {
                min-height: 150px;
                border-radius: 1rem;
                overflow: hidden;
            }

            /* Responsive Design */
            @media (max-width: 768px) {
                .trending__container, .leftovers__container {
                    padding: 0 1rem;
                }

                .recipe-cards {
                    grid-template-columns: 1fr;
                }

                .combination-cards {
                    grid-template-columns: 1fr;
                }

                .ingredient-tags {
                    justify-content: center;
                }

                .gate-button {
                    display: block;
                    margin: 0.5rem 0;
                }
            }

            /* Animation Classes */
            .fade-in-up {
                animation: fadeInUp 0.8s ease forwards;
                opacity: 0;
                transform: translateY(30px);
            }

            @keyframes fadeInUp {
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }

            .stagger-1 { animation-delay: 0.1s; }
            .stagger-2 { animation-delay: 0.2s; }
            .stagger-3 { animation-delay: 0.3s; }
            .stagger-4 { animation-delay: 0.4s; }
            .stagger-5 { animation-delay: 0.5s; }
        </style>

        <style>
            /* About Cards Styles */
            .about__cards {
                display: grid;
                grid-template-columns: 1fr;
                gap: 1.5rem;
                margin-top: 2rem;
            }

            .card {
                border: none;
                border-radius: 1rem;
                box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
                background-color: #fff;
                padding: 1.5rem;
                transition: transform 0.3s ease;
            }

            .card:hover {
                transform: translateY(-5px);
            }

            .card__title {
                color: #222;
                font-weight: 600;
                margin-bottom: 1rem;
                font-size: 1.2rem;
            }

            .card__text {
                color: #444;
                font-size: 0.95rem;
            }

            /* Tips Section Styles */
            .tips {
                padding: 4rem 0;
                background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            }

            .tips__container {
                max-width: 1200px;
                margin: 0 auto;
                padding: 0 1rem;
            }

            .tips__content {
                text-align: center;
            }

            .tips__subtitle {
                color: #666;
                font-size: 1.1rem;
                margin-bottom: 2rem;
                font-weight: 400;
            }

            .tips__card {
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 2rem;
                max-width: 800px;
                margin: 0 auto;
                padding: 2rem;
                background: white;
                border-radius: 2rem;
                box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
                transition: transform 0.3s ease;
            }

            .tips__card:hover {
                transform: translateY(-5px);
            }

            .tips__character {
                flex-shrink: 0;
            }

            .tips__img {
                width: 150px;
                height: 150px;
                object-fit: contain;
                animation: bounce 2s infinite;
                opacity: 0;
                transition: opacity 0.3s ease-in-out;
            }

            .tips__img.loaded {
                opacity: 1;
            }

            .image-fallback {
                width: 150px;
                height: 150px;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                background: linear-gradient(135deg, #f8f9fa, #e9ecef);
                border-radius: 50%;
                border: 4px solid #ea5e18;
                box-shadow: 0 4px 15px rgba(234, 94, 24, 0.3);
            }

            .tips__bubble {
                flex: 1;
                position: relative;
                background: var(--first-color);
                padding: 1.5rem;
                border-radius: 1.5rem;
                border: 3px solid var(--first-color-dark);
            }

            .tips__bubble::before {
                content: '';
                position: absolute;
                left: -15px;
                top: 50%;
                transform: translateY(-50%);
                width: 0;
                height: 0;
                border-top: 15px solid transparent;
                border-bottom: 15px solid transparent;
                border-right: 15px solid var(--first-color-dark);
            }

            .tips__text {
                font-size: 1.1rem;
                color: #333;
                line-height: 1.6;
                margin-bottom: 1rem;
                font-weight: 500;
                transition: opacity 0.3s ease;
                min-height: 3em;
                display: flex;
                align-items: center;
            }

            .tips__button {
                background: var(--first-color-dark);
                color: #333;
                border: none;
                padding: 0.5rem 1rem;
                border-radius: 25px;
                cursor: pointer;
                font-size: 0.9rem;
                font-weight: 600;
                transition: all 0.3s ease;
                display: inline-flex;
                align-items: center;
                gap: 0.5rem;
            }

            .tips__button:hover {
                background: var(--first-color-darken);
                transform: scale(1.05);
            }

            @keyframes bounce {
                0%, 20%, 50%, 80%, 100% {
                    transform: translateY(0);
                }
                40% {
                    transform: translateY(-10px);
                }
                60% {
                    transform: translateY(-5px);
                }
            }

            /* Footer Styles */
            .footer {
                padding: 3rem 0 1rem;
                text-align: center;
                background-color: var(--first-color);
            }

            .footer__container {
                row-gap: 2rem;
            }

            .footer__copy {
                font-size: 0.875rem;
                color: #666;
            }

            @media screen and (min-width: 768px) {
                .about__cards {
                    grid-template-columns: repeat(2, 1fr);
                }

                .footer {
                    padding: 4rem 0 2rem;
                }

                .tips__card {
                    gap: 3rem;
                    padding: 3rem;
                }

                .tips__img {
                    width: 200px;
                    height: 200px;
                }
            }

            @media screen and (max-width: 768px) {
                .tips__card {
                    flex-direction: column;
                    text-align: center;
                    gap: 1.5rem;
                }

                .tips__bubble::before {
                    display: none;
                }

                .tips__img {
                    width: 120px;
                    height: 120px;
                }
            }
        </style>
    </head>
    <body>
        <div id="app">
            <!--===== HEADER =====-->
            <header class="l-header">
            <nav class="nav bd-grid">
                <div>
                   <br><br> <a href="#" class="nav__logo"><img src="{{ url_for('static', filename='images/logo.png') }}" alt="Logo" width="120" height="auto"></a>

                </div>

                <div class="nav__toggle" id="nav-toggle">
                    <i class='bx bx-menu'></i>
                </div>

                <div class="nav__menu" id="nav-menu">
                    <div class="nav__close" id="nav-close">
                        <i class='bx bx-x'></i>
                    </div>

                    <ul class="nav__list">
                        <li class="nav__item"><a href="#home" class="nav__link active">Home</a></li>
                        <li class="nav__item"><a href="#about" class="nav__link">About</a></li>
                        <li class="nav__item"><a href="/login" class="nav__link">Login</a></li>
                        <li class="nav__item"><a href="#contact" class="nav__link">Contact</a></li>
                    </ul>
                </div>
            </nav>
        </header>

        <main class="l-main">
            <!--===== HOME =====-->
            <section class="home" id="home">
                <div class="home__container bd-grid">
                    <div class="home__img">
                        <img src="{{ url_for('static', filename='images/imgA.png') }}" alt="" data-speed="-2" class="move">
                        <img src="{{ url_for('static', filename='images/imgB.png') }}" alt="" data-speed="2" class="move">
                        <img src="{{ url_for('static', filename='images/imgC.png') }}" alt="" data-speed="2" class="move">
                        <img src="{{ url_for('static', filename='images/imgD.png') }}" alt="" data-speed="-2" class="move">
                        <img src="{{ url_for('static', filename='images/imgE.png') }}" alt="" data-speed="-2" class="move">
                        <img src="{{ url_for('static', filename='images/imgF.png') }}" alt="" data-speed="2" class="move">
                    </div>

                    <div class="home__data">
                        <h1 class="home__title">SISA<br> RASA</h1>
                        <p class="home__description">Rasa baru dari Sisa Lama. </p>
                        <a href="/login" class="home__button">Get Started</a>
                    </div>
                </div>
            </section>

            <!--===== ABOUT =====-->
            <section class="about" id="about">
                <div class="about__container bd-grid">
                    <div class="about__data">
                        <h2 class="section__title">About SisaRasa</h2>
                        <p class="about__description">
                            Rasa baru dari sisa lama. At Sisa Rasa, we believe that every ingredient has potential. By combining smart technology with creative cooking, we're changing how people think about food waste. Our goal ? To make eating easier, smarter, and more delicious. 
                            Whether you're a seasoned chef or simply aiming to use up what's in your fridge, Sisa Rasa is your partner in creating delicious meals.
                        </p>

                        <!-- About Cards Section -->
                        <div class="about__cards">
                            <div class="card">
                                <div class="card__body">
                                    <h5 class="card__title">🌱 Our Missions </h5>
                                    <p class="card__text">
                                        Our mission is to encourage households to utilise the most of their leftover ingredients rather than letting them go to waste. We help consumers in transforming their pantry or refrigerator leftovers into mouthwatering dinners with the support of our intelligent recipe recommendation system. 
                                        <br> <br>By doing this, we hope to not only reduce food waste at home but also inspire small, meaningful steps toward a more sustainable future.
                                    </p>
                                </div>
                            </div>
                            <div class="card">
                                <div class="card__body">
                                    <h5 class="card__title">👨‍🍳 What is Sisa Rasa?</h5>
                                    <p class="card__text">
                                        Sisa Rasa is an AI-powered web application that helps users discover recipes based on the leftover ingredients they already have. Using the K-Nearest Neighbors (KNN) algorithm, the system analyzes your input and recommends the closest matching recipes,maximizing usage and minimizing waste.
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!--===== POPULAR RECIPES =====-->
            <section class="trending" id="trending">
                <div class="trending__container bd-grid">
                    <div class="trending__content">
                        <h2 class="section__title">Most Popular Recipes</h2>
                        <p class="trending__subtitle">Discover the community's favorite recipes!</p>

                        <div class="recipe-showcase">
                            <!-- Popular Recipes -->
                            <div class="recipe-section">
                                <h3 class="recipe-section-title">⭐ Most Popular</h3>
                                <div class="recipe-cards" id="popularRecipes">
                                    <!-- Popular recipes will be loaded here -->
                                    <div class="recipe-card-skeleton">
                                        <div class="skeleton-loader"></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="login-gate">
                            <div class="gate-overlay">
                                <div class="gate-content">
                                    <i class='bx bx-lock-alt gate-icon'></i>
                                    <h4>Unlock Full Recipe Details</h4>
                                    <p>Login to see complete recipes, ingredients, and cooking instructions</p>
                                    <a href="/login" class="gate-button">Login Now</a>
                                    <a href="/signup" class="gate-button secondary">Sign Up Free</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!--===== SMART LEFTOVER SOLUTIONS =====-->
            <section class="leftovers" id="leftovers">
                <div class="leftovers__container bd-grid">
                    <div class="leftovers__content">
                        <h2 class="section__title">Smart Leftover Solutions</h2>
                        <p class="leftovers__subtitle">Turn your leftovers into delicious meals!</p>

                        <div class="leftover-showcase">
                            <!-- Common Leftover Ingredients -->
                            <div class="leftover-section">
                                <h3 class="leftover-section-title">🥘 Most Searched Leftovers</h3>
                                <div class="chart-container">
                                    <canvas id="leftoverChart" width="400" height="200"></canvas>
                                </div>
                                <div class="ingredient-tags" id="topLeftovers" style="margin-top: 1rem;">
                                    <!-- Top leftover ingredients will be loaded here as backup -->
                                    <div class="ingredient-tag-skeleton">
                                        <div class="skeleton-loader"></div>
                                    </div>
                                </div>
                            </div>

                            <!-- Leftover Combinations -->
                            <div class="leftover-section">
                                <h3 class="leftover-section-title">💡 Smart Combinations</h3>
                                <div class="combination-cards" id="leftoverCombinations">
                                    <!-- Leftover combinations will be loaded here -->
                                    <div class="combination-card-skeleton">
                                        <div class="skeleton-loader"></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="login-gate">
                            <div class="gate-overlay">
                                <div class="gate-content">
                                    <i class='bx bx-user-plus gate-icon'></i>
                                    <h4>Get Personalized Suggestions</h4>
                                    <p>Sign up to receive leftover solutions based on your cooking history</p>
                                    <a href="/signup" class="gate-button">Join SisaRasa</a>
                                    <a href="/login" class="gate-button secondary">Already a member?</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!--===== FOOD WASTE TIPS =====-->
            <section class="tips" id="tips">
                <div class="tips__container bd-grid">
                    <div class="tips__content">
                        <h2 class="section__title">Food Waste Reduction Tips</h2>
                        <p class="tips__subtitle">Mr. Bob shares amazing facts to reduce food waste at home!</p>

                        <div class="tips__card">
                            <div class="tips__character">
                                <img src="{{ url_for('static', filename='images/mrTips.png') }}"
                                     alt="Mr. Tips"
                                     class="tips__img"
                                     loading="lazy"
                                     onerror="this.style.display='none'; this.nextElementSibling.style.display='block';"
                                     onload="this.style.opacity='1';">
                                <div class="image-fallback" style="display: none; text-align: center; padding: 20px;">
                                    <i class='bx bx-user-circle' style="font-size: 4rem; color: #ea5e18;"></i>
                                    <p style="margin: 0; font-size: 0.9rem; color: #6c757d;">Mr. Tips</p>
                                </div>
                            </div>
                            <div class="tips__bubble">
                                <div class="tips__text" id="tipText">
                                    <!-- Random tip will be loaded here -->
                                </div>
                                <div class="tips__refresh">
                                    <button class="tips__button" onclick="getNewTip()">
                                        <i class='bx bx-refresh'></i> New Tip
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!--===== CONTACT =====-->
            <section class="contact" id="contact">
                <div class="contact__container bd-grid">
                    <div class="contact__data">
                        <h2 class="section__title">Contact Us</h2>
                        <p class="contact__description">Have questions about our recipe recommendation system? Feel free to reach out to us!</p>
                        <div class="contact__info">
                            <div class="contact__item">
                                <i class='bx bx-envelope'></i>
                                <p><EMAIL></p>
                            </div>
                            <div class="contact__item">
                                <i class='bx bx-phone'></i>
                                <p>01135723003</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!--===== FOOTER =====-->
            <footer class="footer">
                <div class="footer__container bd-grid">
                    <p class="footer__copy">© 2025 Sisa Rasa. All rights reserved.</p>
                </div>
            </footer>
        </main>
        </div> <!-- End Vue App -->

        <!--===== GSAP =====-->
        <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.5.1/gsap.min.js"></script>

        <!--===== MAIN JS =====-->
        <script src="{{ url_for('static', filename='main.js') }}"></script>

        <!-- Optional: Confetti JS -->
        <script src="https://cdn.jsdelivr.net/npm/canvas-confetti@1.6.0/dist/confetti.browser.min.js"></script>

        <!-- Vue.js App for Prescriptive Analytics -->
        <script>
            const { createApp } = Vue;

            createApp({
                delimiters: ['${', '}'],
                data() {
                    return {
                        loading: true,
                        leftoverChart: null,
                        analyticsData: {
                            trending_recipes: [],
                            popular_recipes: [],
                            leftover_solutions: {
                                top_leftover_ingredients: [],
                                common_combinations: {}
                            }
                        }
                    }
                },
                mounted() {
                    this.loadPrescriptiveAnalytics();
                },
                methods: {
                    async loadPrescriptiveAnalytics() {
                        try {
                            console.log('Loading prescriptive analytics...');
                            const response = await fetch('/api/analytics/prescriptive');
                            console.log('Response status:', response.status);

                            if (!response.ok) {
                                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                            }

                            const data = await response.json();
                            console.log('Analytics data received:', data);

                            if (data.status === 'success') {
                                this.analyticsData = data.data;
                                this.renderPopularRecipes();
                                this.renderLeftoverSolutions();
                            } else {
                                console.error('API returned error:', data.message);
                                this.loadFallbackData();
                            }
                        } catch (error) {
                            console.error('Error loading prescriptive analytics:', error);
                            this.loadFallbackData();
                        } finally {
                            this.loading = false;
                        }
                    },

                    loadFallbackData() {
                        // Fallback data if API fails
                        this.analyticsData = {
                            popular_recipes: [
                                {
                                    id: 'popular-1',
                                    name: 'Classic Chicken Curry',
                                    ingredients: ['chicken', 'curry powder', 'coconut milk'],
                                    description: 'Highly rated recipe (4.8/5 stars)',
                                    avg_rating: 4.8,
                                    review_count: 156,
                                    prep_time: 45,
                                    difficulty: 'Medium'
                                },
                                {
                                    id: 'popular-2',
                                    name: 'Homemade Fried Rice',
                                    ingredients: ['rice', 'egg', 'soy sauce', 'vegetables'],
                                    description: 'Community favorite (4.6/5 stars)',
                                    avg_rating: 4.6,
                                    review_count: 89,
                                    prep_time: 20,
                                    difficulty: 'Easy'
                                },
                                {
                                    id: 'popular-3',
                                    name: 'Simple Pasta Marinara',
                                    ingredients: ['pasta', 'tomato sauce', 'garlic', 'basil'],
                                    description: 'Quick and delicious (4.4/5 stars)',
                                    avg_rating: 4.4,
                                    review_count: 67,
                                    prep_time: 25,
                                    difficulty: 'Easy'
                                }
                            ],
                            leftover_solutions: {
                                top_leftover_ingredients: [
                                    { name: 'rice', usage_count: 245 },
                                    { name: 'chicken', usage_count: 189 },
                                    { name: 'egg', usage_count: 167 },
                                    { name: 'pasta', usage_count: 134 },
                                    { name: 'vegetables', usage_count: 112 }
                                ],
                                common_combinations: {
                                    'rice_egg': {
                                        ingredients: ['rice', 'egg'],
                                        recipes: ['Fried Rice', 'Egg Rice Bowl', 'Rice Omelette'],
                                        description: 'Perfect for using leftover rice'
                                    },
                                    'pasta_sauce': {
                                        ingredients: ['pasta', 'tomato sauce'],
                                        recipes: ['Pasta Marinara', 'Spaghetti Bolognese', 'Pasta Bake'],
                                        description: 'Quick pasta solutions'
                                    }
                                }
                            }
                        };
                        this.renderPopularRecipes();
                        this.renderLeftoverSolutions();
                    },



                    renderPopularRecipes() {
                        const container = document.getElementById('popularRecipes');
                        if (!container) return;

                        const recipes = this.analyticsData.popular_recipes.slice(0, 3);
                        container.innerHTML = recipes.map((recipe, index) => `
                            <div class="recipe-card fade-in-up stagger-${index + 1}" onclick="handleRecipeClick('${recipe.id || recipe.name.toLowerCase().replace(/\\s+/g, '-')}', '${recipe.name}')">
                                <div class="recipe-card-title">${recipe.name}</div>
                                <div class="recipe-card-description">${recipe.description}</div>
                                <div class="recipe-card-ingredients">
                                    ${recipe.ingredients.slice(0, 4).map(ing =>
                                        `<span class="ingredient-chip">${ing}</span>`
                                    ).join('')}
                                </div>
                                <div class="recipe-card-meta">
                                    <span><i class='bx bx-time'></i> ${recipe.prep_time} min</span>
                                    <span><i class='bx bx-star'></i> ${recipe.avg_rating ? recipe.avg_rating.toFixed(1) : 'N/A'}</span>
                                </div>
                            </div>
                        `).join('');
                    },

                    renderLeftoverSolutions() {
                        // Create chart for top leftover ingredients
                        this.createLeftoverChart();

                        // Render top leftover ingredients as backup/additional info
                        const ingredientsContainer = document.getElementById('topLeftovers');
                        if (ingredientsContainer) {
                            const ingredients = this.analyticsData.leftover_solutions.top_leftover_ingredients.slice(0, 6);
                            if (ingredients.length > 0) {
                                ingredientsContainer.innerHTML = ingredients.map((item, index) => `
                                    <div class="ingredient-tag fade-in-up stagger-${index + 1}">
                                        ${item.name} (${item.usage_count})
                                    </div>
                                `).join('');
                            } else {
                                ingredientsContainer.innerHTML = `
                                    <div class="text-center text-muted">
                                        <p>No leftover data available yet</p>
                                    </div>
                                `;
                            }
                        }

                        // Render leftover combinations
                        const combinationsContainer = document.getElementById('leftoverCombinations');
                        if (combinationsContainer) {
                            const combinations = Object.values(this.analyticsData.leftover_solutions.common_combinations);
                            combinationsContainer.innerHTML = combinations.map((combo, index) => `
                                <div class="combination-card fade-in-up stagger-${index + 1}">
                                    <div class="combination-title">
                                        ${combo.ingredients.join(' + ')}
                                    </div>
                                    <div class="combination-description">
                                        ${combo.description}
                                    </div>
                                    <div class="combination-recipes">
                                        ${combo.recipes.map(recipe =>
                                            `<span class="recipe-chip" onclick="handleRecipeSearch('${recipe}')" style="cursor: pointer; transition: all 0.3s ease;" onmouseover="this.style.transform='scale(1.1)'" onmouseout="this.style.transform='scale(1)'">${recipe}</span>`
                                        ).join('')}
                                    </div>
                                </div>
                            `).join('');
                        }
                    },

                    createLeftoverChart() {
                        const ctx = document.getElementById('leftoverChart');
                        if (!ctx) return;

                        // Destroy existing chart if it exists
                        if (this.leftoverChart) {
                            this.leftoverChart.destroy();
                        }

                        const ingredients = this.analyticsData.leftover_solutions.top_leftover_ingredients.slice(0, 8);

                        if (ingredients.length === 0) {
                            // Show fallback data
                            const fallbackData = [
                                { name: 'rice', usage_count: 245 },
                                { name: 'chicken', usage_count: 189 },
                                { name: 'egg', usage_count: 167 },
                                { name: 'onion', usage_count: 134 },
                                { name: 'garlic', usage_count: 112 }
                            ];
                            this.renderChart(ctx, fallbackData);
                        } else {
                            this.renderChart(ctx, ingredients);
                        }
                    },

                    renderChart(ctx, data) {
                        const labels = data.map(item => item.name);
                        const values = data.map(item => item.usage_count);

                        // Create gradient colors
                        const gradient = ctx.getContext('2d').createLinearGradient(0, 0, 0, 300);
                        gradient.addColorStop(0, '#ea5e18');
                        gradient.addColorStop(1, '#fedf2f');

                        this.leftoverChart = new Chart(ctx, {
                            type: 'doughnut',
                            data: {
                                labels: labels,
                                datasets: [{
                                    data: values,
                                    backgroundColor: [
                                        '#ea5e18',
                                        '#fedf2f',
                                        '#083640',
                                        '#ff7f3f',
                                        '#f9e59a',
                                        '#e1cc7f',
                                        '#0a4550',
                                        '#f1ead1'
                                    ],
                                    borderWidth: 2,
                                    borderColor: '#fff'
                                }]
                            },
                            options: {
                                responsive: true,
                                maintainAspectRatio: false,
                                plugins: {
                                    legend: {
                                        position: 'bottom',
                                        labels: {
                                            padding: 20,
                                            usePointStyle: true,
                                            font: {
                                                family: 'Poppins',
                                                size: 12
                                            }
                                        }
                                    },
                                    tooltip: {
                                        callbacks: {
                                            label: function(context) {
                                                const label = context.label || '';
                                                const value = context.parsed || 0;
                                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                                const percentage = ((value / total) * 100).toFixed(1);
                                                return `${label}: ${value} searches (${percentage}%)`;
                                            }
                                        }
                                    }
                                },
                                animation: {
                                    animateRotate: true,
                                    duration: 1000
                                }
                            }
                        });
                    }
                }
            }).mount('#app');
        </script>

        <!-- Recipe Navigation Functions -->
        <script>
            // === Recipe Click Handlers ===
            function handleRecipeClick(recipeId, recipeName) {
                // Check if user is logged in
                const token = localStorage.getItem('token');

                if (!token) {
                    // Redirect to login page with return URL
                    const returnUrl = encodeURIComponent(window.location.href);
                    window.location.href = `/login?return=${returnUrl}&recipe=${encodeURIComponent(recipeName)}`;
                    return;
                }

                // User is logged in, navigate to recipe details
                navigateToRecipeDetails(recipeId, recipeName);
            }

            function handleRecipeSearch(recipeName) {
                // Check if user is logged in
                const token = localStorage.getItem('token');

                if (!token) {
                    // Redirect to login page with return URL
                    const returnUrl = encodeURIComponent(window.location.href);
                    window.location.href = `/login?return=${returnUrl}&search=${encodeURIComponent(recipeName)}`;
                    return;
                }

                // User is logged in, search for the recipe
                searchForRecipe(recipeName);
            }

            function navigateToRecipeDetails(recipeId, recipeName) {
                // Try to find the recipe in the system first
                fetch(`/api/recipe/${recipeId}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.status === 'ok') {
                            // Show recipe details in a modal
                            showRecipeModal(data.recipe);
                        } else {
                            // Recipe not found, search for similar recipes
                            searchForRecipe(recipeName);
                        }
                    })
                    .catch(error => {
                        console.error('Error fetching recipe:', error);
                        // Fallback to search
                        searchForRecipe(recipeName);
                    });
            }

            function searchForRecipe(recipeName) {
                // Extract potential ingredients from recipe name
                const searchTerms = recipeName.toLowerCase()
                    .replace(/recipe|dish|food/g, '')
                    .split(/[\s,]+/)
                    .filter(term => term.length > 2)
                    .slice(0, 3); // Take first 3 meaningful terms

                if (searchTerms.length > 0) {
                    const searchQuery = searchTerms.join(',');
                    window.location.href = `/search-results?ingredients=${encodeURIComponent(searchQuery)}`;
                } else {
                    // Fallback to dashboard
                    window.location.href = '/dashboard';
                }
            }

            function showRecipeModal(recipe) {
                // Create a detailed recipe modal
                const modalHtml = `
                    <div style="text-align: left; max-height: 500px; overflow-y: auto;">
                        <h6><strong>Ingredients (${recipe.ingredients.length}):</strong></h6>
                        <ul style="margin-bottom: 1.5rem;">
                            ${recipe.ingredients.map(ing => `<li>${ing}</li>`).join('')}
                        </ul>

                        <h6><strong>Instructions:</strong></h6>
                        <ol>
                            ${recipe.steps.map(step => `<li style="margin-bottom: 0.5rem;">${step}</li>`).join('')}
                        </ol>

                        <div style="margin-top: 1.5rem; padding-top: 1rem; border-top: 1px solid #eee;">
                            <div style="display: flex; justify-content: space-between; font-size: 0.9rem; color: #666;">
                                <span><i class='bx bx-time'></i> Prep: ${recipe.prep_time} min</span>
                                <span><i class='bx bx-time'></i> Cook: ${recipe.cook_time} min</span>
                                <span><i class='bx bx-group'></i> Serves: ${recipe.servings}</span>
                                <span><i class='bx bx-world'></i> ${recipe.cuisine}</span>
                            </div>
                        </div>
                    </div>
                `;

                // Use SweetAlert if available, otherwise use native alert
                if (typeof Swal !== 'undefined') {
                    Swal.fire({
                        title: recipe.name,
                        html: modalHtml,
                        width: '700px',
                        confirmButtonColor: '#ea5e18',
                        confirmButtonText: 'Close',
                        showCancelButton: true,
                        cancelButtonText: 'Search Similar',
                        cancelButtonColor: '#6c757d'
                    }).then((result) => {
                        if (result.dismiss === Swal.DismissReason.cancel) {
                            searchForRecipe(recipe.name);
                        }
                    });
                } else {
                    alert(`Recipe: ${recipe.name}\n\nIngredients: ${recipe.ingredients.join(', ')}\n\nInstructions: ${recipe.steps.join(' ')}`);
                }
            }
        </script>

        <!-- Card Animation Script -->
        <script>
            // === Food Waste Tips Data ===
            const foodWasteTips = [
                "💡 Did you know? Every year, 1.3 billion tons of food is wasted worldwide!",
                "🥬 Store leafy greens in airtight containers with damp paper towels to keep them fresh longer.",
                "🍌 Overripe bananas? Use them to make delicious smoothies or banana bread!",
                "❄️ Freeze fresh herbs in ice cubes with olive oil for future cooking use.",
                "🍞 Stale bread can be turned into breadcrumbs or tasty French toast!",
                "🥕 Potato and carrot peels can be roasted into healthy, crispy chips.",
                "🍅 Soft tomatoes are still perfect for sauces, soups, or stir-fry dishes.",
                "🧅 Store onions in a cool, dry place - not in the refrigerator!",
                "🥛 Milk about to expire? Use it to make pancakes, cakes, or smoothies.",
                "🍋 Squeeze lemon juice and freeze it in ice trays for long-term use.",
                "🥒 Wilted cucumbers can become natural face masks or infused water!",
                "🌿 Regrow vegetable stems like green onions by placing them in water.",
                "🍚 Leftover rice? Try making fried rice, porridge, or even rice pudding dessert!",
                "🥗 Blend almost-spoiled vegetables into nutritious green smoothies.",
                "📅 Use the 'first in, first out' system - use older food before newer items.",
                "🥖 Day-old bread makes excellent croutons when toasted with herbs and oil.",
                "🍎 Brown apple slices are perfect for baking pies, muffins, or applesauce.",
                "🥔 Sprouting potatoes? Remove sprouts and eyes, then cook normally - they're still safe!",
                "🧄 Garlic cloves starting to sprout can still be used - just remove the green center.",
                "🥦 Broccoli stems are edible! Peel and chop them for stir-fries or soups."
            ];

            let currentTipIndex = 0;

            // === Get Random Tip Function ===
            function getNewTip() {
                const tipText = document.getElementById('tipText');
                const randomIndex = Math.floor(Math.random() * foodWasteTips.length);

                // Add fade out effect
                tipText.style.opacity = '0';

                setTimeout(() => {
                    tipText.textContent = foodWasteTips[randomIndex];
                    tipText.style.opacity = '1';
                    currentTipIndex = randomIndex;
                }, 300);
            }

            // === Image Loading Handler ===
            function handleImageLoad() {
                // Handle all images with proper loading
                const images = document.querySelectorAll('img[loading="lazy"]');
                images.forEach(img => {
                    if (img.complete) {
                        img.style.opacity = '1';
                        img.classList.add('loaded');
                    } else {
                        img.addEventListener('load', function() {
                            this.style.opacity = '1';
                            this.classList.add('loaded');
                        });

                        img.addEventListener('error', function() {
                            console.warn('Image failed to load:', this.src);
                            this.style.display = 'none';
                            const fallback = this.nextElementSibling;
                            if (fallback && fallback.classList.contains('image-fallback')) {
                                fallback.style.display = 'flex';
                            }
                        });
                    }
                });
            }

            // === Initialize on page load ===
            document.addEventListener('DOMContentLoaded', function() {
                // Load initial tip
                getNewTip();

                // Handle image loading
                handleImageLoad();

                // === Scroll fade-in for cards ===
                const cards = document.querySelectorAll('.card, .tips__card');

                const observer = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            entry.target.classList.add('animate__animated', 'animate__fadeInUp');
                            observer.unobserve(entry.target);
                        }
                    });
                }, {
                    threshold: 0.2
                });

                cards.forEach(card => {
                    observer.observe(card);
                });

                // === Fun: Confetti on heading click ===
                const sectionTitles = document.querySelectorAll('.section__title');
                sectionTitles.forEach(title => {
                    title.addEventListener('click', () => {
                        confetti();
                    });
                });

                // === Auto-refresh tip every 10 seconds ===
                setInterval(getNewTip, 10000);
            });
        </script>
    </body>
</html>